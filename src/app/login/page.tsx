"use client";

import { zod<PERSON>esolver } from "@hookform/resolvers/zod";
import Image from "next/image";
import Link from "next/link";
import { useForm } from "react-hook-form";
import { z } from "zod";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle,
} from "@/components/ui/card";
import { Checkbox } from "@/components/ui/checkbox";
import {
	Form,
	FormControl,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";

const loginSchema = z.object({
	email: z.string().email({ message: "Please enter a valid email address" }),
	password: z
		.string()
		.min(1, { message: "Password is required" })
		.min(4, { message: "Password must be at least 4 characters" }),
	keepSignedIn: z.boolean().default(false),
});

type LoginFormValues = z.infer<typeof loginSchema>;

export default function LoginPage() {
	const form = useForm<LoginFormValues>({
		resolver: zodResolver(loginSchema),
		defaultValues: {
			email: "",
			password: "",
			keepSignedIn: false,
		},
	});

	function onSubmit(values: LoginFormValues) {
		console.log("Login form submitted:", values);
	}

	return (
		<div className="min-h-screen flex">
			{/* Left side - Login Form */}
			<div className="flex-1 flex items-center justify-center p-8 bg-background">
				<div className="w-full max-w-md space-y-8">
					{/* Logo */}
					<div className="flex justify-center">
						<Image
							src="/pukpara-logo.png"
							alt="Pukpara Logo"
							width={120}
							height={40}
							className="h-10 w-auto"
						/>
					</div>

					{/* Sign In Card */}
					<Card className="border-0 shadow-none">
						<CardHeader className="space-y-1 text-center pb-4">
							<CardTitle className="text-2xl font-semibold text-foreground">
								Sign In
							</CardTitle>
							<CardDescription className="text-muted-foreground">
								Kindly enter the following details to login.
							</CardDescription>
						</CardHeader>
						<CardContent>
							<Form {...form}>
								<form
									onSubmit={form.handleSubmit(onSubmit)}
									className="space-y-4"
								>
									{/* Email Field */}
									<FormField
										control={form.control}
										name="email"
										render={({ field }) => (
											<FormItem>
												<FormLabel className="text-sm font-medium text-foreground">
													Email
												</FormLabel>
												<FormControl>
													<Input
														type="email"
														placeholder="Email"
														className="h-12 border-border bg-background"
														{...field}
													/>
												</FormControl>
												<FormMessage />
											</FormItem>
										)}
									/>

									{/* Password Field */}
									<FormField
										control={form.control}
										name="password"
										render={({ field }) => (
											<FormItem>
												<FormLabel className="text-sm font-medium text-foreground">
													Password
												</FormLabel>
												<FormControl>
													<Input
														type="password"
														placeholder="Password"
														className="h-12 border-border bg-background"
														{...field}
													/>
												</FormControl>
												<FormMessage />
											</FormItem>
										)}
									/>

									{/* Keep me signed in & Forgot password */}
									<div className="flex items-center justify-between">
										<FormField
											control={form.control}
											name="keepSignedIn"
											render={({ field }) => (
												<FormItem className="flex flex-row items-start space-x-3 space-y-0">
													<FormControl>
														<Checkbox
															checked={field.value}
															onCheckedChange={field.onChange}
														/>
													</FormControl>
													<div className="space-y-1 leading-none">
														<FormLabel className="text-sm font-normal text-foreground">
															Keep me signed in
														</FormLabel>
													</div>
												</FormItem>
											)}
										/>
										<Link
											href="/forgot-password"
											className="text-sm text-primary hover:underline"
										>
											Forgot password?
										</Link>
									</div>

									{/* Sign In Button */}
									<Button
										type="submit"
										className="w-full h-12 bg-primary hover:bg-primary/90 text-primary-foreground font-medium"
									>
										Sign In
									</Button>
								</form>
							</Form>

							{/* Sign Up Link */}
							<div className="mt-6 text-center">
								<span className="text-sm text-muted-foreground">
									Not a member yet?{" "}
									<Link
										href="/register"
										className="text-primary hover:underline font-medium"
									>
										Sign up
									</Link>
								</span>
							</div>

							{/* Footer Links */}
							<div className="mt-8 flex justify-center space-x-6 text-sm text-muted-foreground">
								<Link href="/terms" className="hover:text-primary">
									Terms
								</Link>
								<Link href="/plans" className="hover:text-primary">
									Plans
								</Link>
								<Link href="/contact" className="hover:text-primary">
									Contact Us
								</Link>
							</div>
						</CardContent>
					</Card>
				</div>
			</div>

			{/* Right side - Background Image */}
			<div className="hidden lg:flex flex-1 relative">
				<Image
					src="/auth-image1.jpg"
					alt="Authentication background"
					fill
					className="object-cover"
					priority
				/>
			</div>
		</div>
	);
}
